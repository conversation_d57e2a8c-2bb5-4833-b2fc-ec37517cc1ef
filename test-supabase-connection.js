// Test Supabase connection and database schema
const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

console.log('🔍 Testing Supabase Connection...')
console.log('URL:', supabaseUrl)
console.log('Key:', supabaseKey ? `${supabaseKey.substring(0, 20)}...` : 'NOT SET')

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase credentials')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseKey)

async function testConnection() {
  try {
    console.log('\n🔄 Testing basic connection...')
    
    // Test basic connection
    const { data, error } = await supabase
      .from('products')
      .select('count', { count: 'exact', head: true })
    
    if (error) {
      console.error('❌ Connection error:', error)
      return false
    }
    
    console.log('✅ Connection successful')
    console.log('📊 Products count:', data)
    
    // Test actual data fetch
    console.log('\n🔄 Testing data fetch...')
    const { data: products, error: fetchError } = await supabase
      .from('products')
      .select('*')
      .limit(5)
    
    if (fetchError) {
      console.error('❌ Data fetch error:', fetchError)
      return false
    }
    
    console.log('✅ Data fetch successful')
    console.log('📦 Sample products:', products?.length || 0)
    
    if (products && products.length > 0) {
      console.log('📋 First product:', JSON.stringify(products[0], null, 2))
    }
    
    // Test other tables
    console.log('\n🔄 Testing other tables...')
    
    const tables = ['customers', 'customer_debts', 'customer_payments', 'customer_balances']
    
    for (const table of tables) {
      try {
        const { data, error } = await supabase
          .from(table)
          .select('count', { count: 'exact', head: true })
        
        if (error) {
          console.error(`❌ ${table} error:`, error.message)
        } else {
          console.log(`✅ ${table}: ${data || 0} records`)
        }
      } catch (err) {
        console.error(`❌ ${table} exception:`, err.message)
      }
    }
    
    return true
    
  } catch (error) {
    console.error('❌ Test failed:', error)
    return false
  }
}

testConnection().then(success => {
  if (success) {
    console.log('\n🎉 All tests passed!')
  } else {
    console.log('\n💥 Some tests failed!')
    process.exit(1)
  }
})
