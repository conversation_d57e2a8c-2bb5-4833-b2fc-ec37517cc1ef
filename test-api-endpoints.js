// Test API endpoints directly
const http = require('http')

function testEndpoint(path, port = 3000) {
  return new Promise((resolve) => {
    const options = {
      hostname: 'localhost',
      port: port,
      path: path,
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    }

    const req = http.request(options, (res) => {
      let data = ''
      
      res.on('data', (chunk) => {
        data += chunk
      })
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data)
          resolve({
            status: res.statusCode,
            data: jsonData,
            success: res.statusCode >= 200 && res.statusCode < 300
          })
        } catch (error) {
          resolve({
            status: res.statusCode,
            data: data,
            success: false,
            error: 'Invalid JSON response'
          })
        }
      })
    })

    req.on('error', (error) => {
      resolve({
        status: 0,
        success: false,
        error: error.message
      })
    })

    req.setTimeout(5000, () => {
      req.destroy()
      resolve({
        status: 0,
        success: false,
        error: 'Request timeout'
      })
    })

    req.end()
  })
}

async function testAllEndpoints() {
  console.log('🔍 Testing API endpoints...')
  
  const endpoints = [
    '/api/products',
    '/api/debts', 
    '/api/payments',
    '/api/customer-balances'
  ]
  
  // Try both port 3000 and 3001
  const ports = [3000, 3001]
  
  for (const port of ports) {
    console.log(`\n🔄 Testing on port ${port}...`)
    
    let anySuccess = false
    
    for (const endpoint of endpoints) {
      console.log(`\n📡 Testing ${endpoint}...`)
      
      const result = await testEndpoint(endpoint, port)
      
      if (result.success) {
        console.log(`✅ ${endpoint}: Status ${result.status}`)
        if (result.data && typeof result.data === 'object') {
          if (result.data.success !== undefined) {
            console.log(`   Success: ${result.data.success}`)
          }
          if (result.data.data) {
            const dataKeys = Object.keys(result.data.data)
            console.log(`   Data keys: ${dataKeys.join(', ')}`)
          }
          if (result.data.error) {
            console.log(`   Error: ${result.data.error}`)
          }
        }
        anySuccess = true
      } else {
        console.log(`❌ ${endpoint}: Status ${result.status}`)
        console.log(`   Error: ${result.error || 'Unknown error'}`)
        if (result.data && typeof result.data === 'string') {
          console.log(`   Response: ${result.data.substring(0, 200)}...`)
        }
      }
    }
    
    if (anySuccess) {
      console.log(`\n🎉 Found working server on port ${port}`)
      break
    } else {
      console.log(`\n💥 No working endpoints on port ${port}`)
    }
  }
}

testAllEndpoints().catch(console.error)
